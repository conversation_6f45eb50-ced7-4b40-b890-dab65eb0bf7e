import { default as add } from "./add";
import { default as ceil } from "./ceil";
import { default as divide } from "./divide";
import { default as floor } from "./floor";
import { default as max } from "./max";
import { default as maxBy } from "./maxBy";
import { default as mean } from "./mean";
import { default as meanBy } from "./meanBy";
import { default as min } from "./min";
import { default as minBy } from "./minBy";
import { default as multiply } from "./multiply";
import { default as round } from "./round";
import { default as subtract } from "./subtract";
import { default as sum } from "./sum";
import { default as sumBy } from "./sumBy";

export { default } from "./math.default";
